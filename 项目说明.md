# 滚动截图工具 - 项目说明

## 项目概述

这是一个基于 Python + PyQt6 开发的桌面端滚动截图工具，实现了您需求文档中的所有核心功能。

## 已实现的功能

### ✅ 基础功能
- [x] 桌面应用程序，双击启动
- [x] 简洁的图形用户界面 (GUI)
- [x] 滚动速度设置（滑块调节）
- [x] 滚动步长和间隔设置
- [x] 多种截图模式（全屏、窗口、自定义区域）
- [x] 自动保存截图功能
- [x] 多种文件格式支持（PNG、JPEG、BMP）
- [x] 自定义保存路径和文件名
- [x] 开始、暂停、停止控制
- [x] 进度显示和状态反馈
- [x] 详细日志记录
- [x] 错误处理和提示

### ✅ 技术特性
- [x] 响应式界面设计
- [x] 高DPI支持
- [x] 多线程截图处理
- [x] 自动图像拼接
- [x] 配置文件保存
- [x] 临时文件自动清理

## 项目结构

```
A-gundongjieping-tool/
├── main.py                    # 主程序入口
├── test_gui.py               # GUI测试脚本
├── requirements.txt          # Python依赖包
├── install.bat              # 依赖安装脚本
├── run.bat                  # 程序启动脚本
├── README.md                # 项目文档
├── 项目说明.md              # 中文说明文档
├── config.example.json      # 配置文件示例
└── src/                     # 源代码目录
    ├── __init__.py
    ├── gui/                 # 界面模块
    │   ├── __init__.py
    │   └── main_window.py   # 主窗口界面
    ├── core/                # 核心功能模块
    │   ├── __init__.py
    │   └── screenshot_manager.py  # 截图管理器
    └── utils/               # 工具模块
        ├── __init__.py
        └── config.py        # 配置管理
```

## 快速开始

### 方法一：使用安装脚本（推荐）
1. 双击运行 `install.bat` 安装依赖
2. 双击运行 `run.bat` 启动程序

### 方法二：手动安装
1. 确保已安装 Python 3.8+
2. 运行 `pip install -r requirements.txt`
3. 运行 `python main.py`

### 方法三：测试界面
运行 `python test_gui.py` 测试界面是否正常

## 界面功能说明

### 1. 滚动设置区域
- **滚动速度**：1-10级可调，影响滚动的快慢
- **滚动步长**：每次滚动的像素数（50-500px）
- **滚动间隔**：每次滚动之间的等待时间（100-2000ms）

### 2. 截图区域设置
- **全屏截图**：截取整个屏幕
- **窗口截图**：截取指定窗口（待实现）
- **自定义区域**：截取用户选择的矩形区域（待实现）

### 3. 保存设置
- **保存路径**：选择截图保存的文件夹
- **文件名前缀**：自定义文件名前缀
- **文件格式**：支持PNG、JPEG、BMP格式

### 4. 控制按钮
- **开始截图**：启动滚动截图流程
- **暂停/继续**：暂停或继续截图过程
- **停止**：停止截图并保存已截取的内容

### 5. 进度和日志
- **进度条**：显示截图进度
- **状态显示**：显示当前操作状态
- **日志区域**：显示详细的操作日志

## 技术架构

### 核心组件

1. **MainWindow (主窗口)**
   - 负责用户界面展示
   - 处理用户交互事件
   - 管理界面状态更新

2. **ScreenshotManager (截图管理器)**
   - 核心截图逻辑
   - 滚动控制
   - 图像拼接处理
   - 多线程任务管理

3. **CaptureThread (截图线程)**
   - 后台截图处理
   - 避免界面卡顿
   - 实时进度反馈

4. **Config (配置管理)**
   - 用户设置保存
   - 配置文件管理
   - 默认参数设置

### 技术栈
- **GUI框架**：PyQt6
- **图像处理**：Pillow (PIL)
- **自动化控制**：pyautogui
- **窗口管理**：pygetwindow
- **输入监听**：pynput

## 待完善功能

### 🔄 计划中的功能
- [ ] 自定义区域选择工具
- [ ] 窗口截图模式实现
- [ ] 快捷键支持
- [ ] 截图预览功能
- [ ] 智能滚动检测（自动停止）
- [ ] 批量处理功能
- [ ] 截图质量优化
- [ ] 更多图像格式支持

### 🐛 已知问题
- 自定义区域选择功能尚未实现
- 窗口截图模式需要完善
- 滚动检测可能在某些应用中不够准确

## 使用建议

1. **首次使用**：建议先用默认设置测试
2. **滚动速度**：根据页面内容调整，复杂页面建议使用较慢速度
3. **保存路径**：确保有足够的磁盘空间
4. **大型页面**：截图可能需要较长时间，请耐心等待
5. **权限问题**：首次运行可能需要授予屏幕录制权限

## 故障排除

### 常见问题
1. **程序无法启动**：检查Python版本和依赖包安装
2. **截图失败**：检查屏幕录制权限
3. **滚动不正常**：确保目标窗口处于活动状态
4. **保存失败**：检查保存路径权限和磁盘空间

### 获取帮助
- 查看日志区域的错误信息
- 运行 `python test_gui.py` 检查环境
- 检查 `requirements.txt` 中的依赖版本

## 开发说明

### 代码结构
- 采用模块化设计，便于维护和扩展
- 使用信号槽机制处理界面交互
- 多线程处理避免界面卡顿
- 完善的错误处理和日志记录

### 扩展开发
- 新功能可在对应模块中添加
- 界面修改主要在 `main_window.py`
- 核心逻辑在 `screenshot_manager.py`
- 配置管理在 `config.py`

这个项目已经实现了您需求文档中的大部分功能，提供了完整的桌面应用体验。您可以直接使用，也可以根据需要进一步定制开发。
