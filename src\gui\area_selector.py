#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域选择器 - 用于选择截图区域
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QWidget, QLabel, QVBoxLayout, QHBoxLayout, QPushButton
)
from PyQt6.QtCore import Qt, QRect, pyqtSignal, QTimer
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QPixmap, QScreen
from PIL import ImageGrab


class AreaSelector(QWidget):
    """区域选择器窗口"""
    
    area_selected = pyqtSignal(tuple)  # 发送选中的区域 (x, y, width, height)
    selection_cancelled = pyqtSignal()  # 取消选择信号
    
    def __init__(self):
        super().__init__()
        self.start_point = None
        self.end_point = None
        self.selecting = False
        self.background_pixmap = None
        
        self.init_ui()
        self.capture_screen()
        
    def init_ui(self):
        """初始化界面"""
        # 设置窗口为全屏无边框
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 获取屏幕尺寸
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        self.setGeometry(screen_geometry)
        
        # 设置鼠标追踪
        self.setMouseTracking(True)
        
        # 设置光标
        self.setCursor(Qt.CursorShape.CrossCursor)
        
        # 创建提示标签
        self.create_instruction_label()
        
    def create_instruction_label(self):
        """创建指令标签"""
        self.instruction_label = QLabel(self)
        self.instruction_label.setText(
            "拖拽鼠标选择截图区域 | ESC取消 | Enter确认"
        )
        self.instruction_label.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 180);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        self.instruction_label.adjustSize()
        
        # 将标签放在屏幕顶部中央
        label_x = (self.width() - self.instruction_label.width()) // 2
        self.instruction_label.move(label_x, 20)
        
    def capture_screen(self):
        """捕获屏幕背景"""
        try:
            # 使用PIL截取屏幕
            screen_img = ImageGrab.grab()
            
            # 转换为QPixmap
            screen_img.save("temp_screen.png")
            self.background_pixmap = QPixmap("temp_screen.png")
            
            # 删除临时文件
            import os
            if os.path.exists("temp_screen.png"):
                os.remove("temp_screen.png")
                
        except Exception as e:
            print(f"截取屏幕失败: {e}")
            
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        
        # 绘制背景（截图）
        if self.background_pixmap:
            painter.drawPixmap(0, 0, self.background_pixmap)
            
        # 绘制半透明遮罩
        painter.fillRect(self.rect(), QColor(0, 0, 0, 100))
        
        # 如果正在选择，绘制选择框
        if self.start_point and self.end_point:
            selection_rect = QRect(self.start_point, self.end_point).normalized()
            
            # 清除选择区域的遮罩（显示原始截图）
            painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_Clear)
            painter.fillRect(selection_rect, QColor(0, 0, 0, 0))
            
            # 重新绘制选择区域的背景
            painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceOver)
            if self.background_pixmap:
                painter.drawPixmap(selection_rect, self.background_pixmap, selection_rect)
            
            # 绘制选择框边框
            pen = QPen(QColor(255, 0, 0), 2, Qt.PenStyle.SolidLine)
            painter.setPen(pen)
            painter.drawRect(selection_rect)
            
            # 绘制选择框信息
            self.draw_selection_info(painter, selection_rect)
            
    def draw_selection_info(self, painter, rect):
        """绘制选择框信息"""
        info_text = f"区域: {rect.width()} x {rect.height()}"
        
        # 设置文字样式
        painter.setPen(QPen(QColor(255, 255, 255), 1))
        painter.setBrush(QBrush(QColor(0, 0, 0, 180)))
        
        # 计算文字位置
        text_rect = painter.fontMetrics().boundingRect(info_text)
        text_x = rect.x()
        text_y = rect.y() - text_rect.height() - 5
        
        # 如果文字会超出屏幕顶部，则显示在选择框内部
        if text_y < 0:
            text_y = rect.y() + 20
            
        # 绘制文字背景
        bg_rect = QRect(text_x, text_y, text_rect.width() + 10, text_rect.height() + 5)
        painter.fillRect(bg_rect, QColor(0, 0, 0, 180))
        
        # 绘制文字
        painter.drawText(text_x + 5, text_y + text_rect.height(), info_text)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.start_point = event.position().toPoint()
            self.end_point = self.start_point
            self.selecting = True
            self.update()
            
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.selecting:
            self.end_point = event.position().toPoint()
            self.update()
            
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.selecting:
            self.selecting = False
            
            if self.start_point and self.end_point:
                # 计算选择区域
                selection_rect = QRect(self.start_point, self.end_point).normalized()
                
                # 确保选择区域有效
                if selection_rect.width() > 10 and selection_rect.height() > 10:
                    area = (
                        selection_rect.x(),
                        selection_rect.y(),
                        selection_rect.width(),
                        selection_rect.height()
                    )
                    self.area_selected.emit(area)
                    self.close()
                else:
                    # 选择区域太小，重置
                    self.start_point = None
                    self.end_point = None
                    self.update()
                    
    def keyPressEvent(self, event):
        """键盘事件"""
        if event.key() == Qt.Key.Key_Escape:
            # ESC键取消选择
            self.selection_cancelled.emit()
            self.close()
        elif event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            # Enter键确认选择
            if self.start_point and self.end_point:
                selection_rect = QRect(self.start_point, self.end_point).normalized()
                if selection_rect.width() > 10 and selection_rect.height() > 10:
                    area = (
                        selection_rect.x(),
                        selection_rect.y(),
                        selection_rect.width(),
                        selection_rect.height()
                    )
                    self.area_selected.emit(area)
                    self.close()
                    
    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        # 确保窗口获得焦点
        self.activateWindow()
        self.raise_()


def test_area_selector():
    """测试区域选择器"""
    app = QApplication(sys.argv)
    
    def on_area_selected(area):
        print(f"选择的区域: x={area[0]}, y={area[1]}, width={area[2]}, height={area[3]}")
        app.quit()
        
    def on_selection_cancelled():
        print("取消选择")
        app.quit()
        
    selector = AreaSelector()
    selector.area_selected.connect(on_area_selected)
    selector.selection_cancelled.connect(on_selection_cancelled)
    selector.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    test_area_selector()
