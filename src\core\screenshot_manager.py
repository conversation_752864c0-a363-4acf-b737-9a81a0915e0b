#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
截图管理器 - 负责截图的核心逻辑
"""

import os
import time
from datetime import datetime
from typing import Tuple, List, Optional
from PyQt6.QtCore import QObject, pyqtSignal, QTimer, QThread
from PIL import Image, ImageGrab
import pyautogui


class ScreenshotManager(QObject):
    """截图管理器类"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)  # 进度更新信号
    status_updated = pyqtSignal(str)    # 状态更新信号
    log_message = pyqtSignal(str)       # 日志消息信号
    capture_completed = pyqtSignal(str) # 截图完成信号
    error_occurred = pyqtSignal(str)    # 错误发生信号
    
    def __init__(self):
        super().__init__()
        self.is_capturing = False
        self.is_paused = False
        self.capture_thread = None
        self.screenshots = []  # 存储截图列表
        
        # 截图参数
        self.scroll_speed = 5
        self.scroll_step = 100
        self.scroll_interval = 500
        self.capture_mode = "全屏截图"
        self.save_path = os.path.expanduser("~/Desktop")
        self.filename_prefix = "scroll_capture"
        self.file_format = "PNG"
        self.capture_area = None  # (x, y, width, height)
        
        # 初始化pyautogui设置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
    def set_scroll_settings(self, speed: int, step: int, interval: int):
        """设置滚动参数"""
        self.scroll_speed = speed
        self.scroll_step = step
        self.scroll_interval = interval
        
    def set_capture_mode(self, mode: str):
        """设置截图模式"""
        self.capture_mode = mode
        
    def set_capture_area(self, area: Optional[Tuple[int, int, int, int]]):
        """设置截图区域"""
        self.capture_area = area
        
    def set_save_settings(self, path: str, prefix: str, format_type: str):
        """设置保存参数"""
        self.save_path = path
        self.filename_prefix = prefix
        self.file_format = format_type
        
    def start_capture(self):
        """开始截图"""
        if self.is_capturing:
            return
            
        self.is_capturing = True
        self.is_paused = False
        self.screenshots.clear()
        
        # 创建并启动截图线程
        self.capture_thread = CaptureThread(self)
        self.capture_thread.screenshot_taken.connect(self.on_screenshot_taken)
        self.capture_thread.capture_finished.connect(self.on_capture_finished)
        self.capture_thread.error_occurred.connect(self.on_error_occurred)
        self.capture_thread.start()
        
        self.log_message.emit("开始滚动截图")
        
    def pause_capture(self):
        """暂停/继续截图"""
        if not self.is_capturing:
            return
            
        self.is_paused = not self.is_paused
        if self.is_paused:
            self.log_message.emit("截图已暂停")
        else:
            self.log_message.emit("继续截图")
            
    def stop_capture(self):
        """停止截图"""
        if not self.is_capturing:
            return
            
        self.is_capturing = False
        self.is_paused = False
        
        if self.capture_thread and self.capture_thread.isRunning():
            self.capture_thread.terminate()
            self.capture_thread.wait()
            
        self.log_message.emit("截图已停止")
        
    def on_screenshot_taken(self, screenshot_path: str):
        """处理单张截图完成"""
        self.screenshots.append(screenshot_path)
        self.log_message.emit(f"截取第 {len(self.screenshots)} 张图片")
        
    def on_capture_finished(self, final_image_path: str):
        """处理截图完成"""
        self.is_capturing = False
        self.is_paused = False
        self.capture_completed.emit(final_image_path)
        self.log_message.emit(f"截图完成，保存至: {final_image_path}")
        
    def on_error_occurred(self, error_message: str):
        """处理错误"""
        self.is_capturing = False
        self.is_paused = False
        self.error_occurred.emit(error_message)
        self.log_message.emit(f"错误: {error_message}")


class CaptureThread(QThread):
    """截图线程类"""
    
    screenshot_taken = pyqtSignal(str)
    capture_finished = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, manager: ScreenshotManager):
        super().__init__()
        self.manager = manager
        
    def run(self):
        """线程运行方法"""
        try:
            self.perform_scroll_capture()
        except Exception as e:
            self.error_occurred.emit(str(e))
            
    def perform_scroll_capture(self):
        """执行滚动截图"""
        screenshots = []
        screenshot_count = 0
        previous_screenshot = None
        duplicate_count = 0
        max_duplicates = 3  # 连续相同截图的最大数量

        # 等待用户准备
        self.manager.log_message.emit("准备开始截图，请确保目标窗口处于活动状态...")
        time.sleep(3)

        # 获取初始鼠标位置
        initial_pos = pyautogui.position()

        try:
            while self.manager.is_capturing:
                # 检查暂停状态
                while self.manager.is_paused and self.manager.is_capturing:
                    time.sleep(0.1)

                if not self.manager.is_capturing:
                    break

                # 截取当前屏幕
                screenshot = self.take_screenshot()
                if screenshot:
                    # 检查是否与上一张截图相同（判断是否到达底部）
                    if self.is_duplicate_screenshot(screenshot, previous_screenshot):
                        duplicate_count += 1
                        self.manager.log_message.emit(f"检测到重复截图 ({duplicate_count}/{max_duplicates})")

                        if duplicate_count >= max_duplicates:
                            self.manager.log_message.emit("检测到页面底部，停止截图")
                            break
                    else:
                        duplicate_count = 0

                        # 保存截图
                        temp_path = os.path.join(
                            self.manager.save_path,
                            f"temp_{screenshot_count:04d}.png"
                        )
                        screenshot.save(temp_path)
                        screenshots.append(screenshot)  # 保存PIL图像对象
                        screenshot_count += 1

                        self.screenshot_taken.emit(temp_path)
                        self.manager.log_message.emit(f"已截取第 {screenshot_count} 张图片")
                        previous_screenshot = screenshot.copy()

                        # 更新进度（估算）
                        if screenshot_count <= 50:  # 前50张按比例更新
                            progress = min(int(screenshot_count * 1.5), 75)
                            self.manager.progress_updated.emit(progress)

                # 执行滚动
                if not self.scroll_down():
                    self.manager.log_message.emit("滚动失败，停止截图")
                    break

                # 等待滚动完成和页面稳定
                time.sleep(self.manager.scroll_interval / 1000.0)

                # 防止无限循环
                if screenshot_count > 200:
                    self.manager.log_message.emit("达到最大截图数量，停止截图")
                    break

            # 拼接所有截图
            if screenshots:
                self.manager.log_message.emit(f"开始拼接 {len(screenshots)} 张截图...")
                final_image_path = self.stitch_screenshots_optimized(screenshots)
                self.capture_finished.emit(final_image_path)
            else:
                self.error_occurred.emit("未能截取任何图片")

        except Exception as e:
            raise e
            
    def take_screenshot(self) -> Optional[Image.Image]:
        """截取屏幕"""
        try:
            if self.manager.capture_mode == "全屏截图":
                return ImageGrab.grab()
            elif (self.manager.capture_mode in ["自定义区域", "窗口截图"] and
                  self.manager.capture_area):
                x, y, w, h = self.manager.capture_area
                return ImageGrab.grab(bbox=(x, y, x + w, y + h))
            else:
                # 默认全屏
                return ImageGrab.grab()
        except Exception as e:
            self.error_occurred.emit(f"截图失败: {str(e)}")
            return None
            
    def scroll_down(self) -> bool:
        """向下滚动"""
        try:
            # 获取当前鼠标位置
            current_pos = pyautogui.position()

            # 滚动前先点击确保窗口获得焦点
            pyautogui.click(current_pos.x, current_pos.y)
            time.sleep(0.1)

            # 执行滚动（负数表示向下滚动）
            scroll_amount = -self.manager.scroll_step // 10  # pyautogui的滚动单位较小
            pyautogui.scroll(scroll_amount)

            return True
        except Exception as e:
            self.error_occurred.emit(f"滚动失败: {str(e)}")
            return False

    def is_duplicate_screenshot(self, current_img, previous_img) -> bool:
        """检查两张截图是否相同"""
        if previous_img is None:
            return False

        try:
            # 比较图像尺寸
            if current_img.size != previous_img.size:
                return False

            # 简单的像素差异检测
            from PIL import ImageChops
            diff = ImageChops.difference(current_img, previous_img)

            # 计算差异的统计信息
            stat = diff.getextrema()

            # 如果所有通道的最大差异都很小，认为是重复图片
            max_diff = max([channel[1] for channel in stat])
            return max_diff < 10  # 阈值可调整

        except Exception:
            return False
            
    def stitch_screenshots(self, screenshot_paths: List[str]) -> str:
        """拼接截图"""
        try:
            if not screenshot_paths:
                raise ValueError("没有截图需要拼接")
                
            # 加载所有图片
            images = []
            for path in screenshot_paths:
                img = Image.open(path)
                images.append(img)
                
            # 计算拼接后的图片尺寸
            total_height = sum(img.height for img in images)
            max_width = max(img.width for img in images)
            
            # 创建拼接后的图片
            stitched_image = Image.new('RGB', (max_width, total_height))
            
            # 拼接图片
            y_offset = 0
            for img in images:
                stitched_image.paste(img, (0, y_offset))
                y_offset += img.height
                
            # 保存最终图片
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.manager.filename_prefix}_{timestamp}.{self.manager.file_format.lower()}"
            final_path = os.path.join(self.manager.save_path, filename)
            
            stitched_image.save(final_path, self.manager.file_format)
            
            return final_path
            
        except Exception as e:
            raise Exception(f"拼接图片失败: {str(e)}")
            
    def stitch_screenshots_optimized(self, images: List[Image.Image]) -> str:
        """优化的截图拼接方法"""
        try:
            if not images:
                raise ValueError("没有截图需要拼接")

            self.manager.log_message.emit("正在分析图像重叠区域...")

            # 检测并移除重叠区域
            processed_images = self.remove_overlaps(images)

            self.manager.log_message.emit(f"处理后剩余 {len(processed_images)} 个图像片段")

            # 计算拼接后的图片尺寸
            total_height = sum(img.height for img in processed_images)
            max_width = max(img.width for img in processed_images)

            self.manager.log_message.emit(f"创建最终图像: {max_width}x{total_height}")

            # 创建拼接后的图片
            stitched_image = Image.new('RGB', (max_width, total_height), 'white')

            # 拼接图片
            y_offset = 0
            for i, img in enumerate(processed_images):
                stitched_image.paste(img, (0, y_offset))
                y_offset += img.height

                # 更新进度
                progress = int((i + 1) / len(processed_images) * 100)
                self.manager.progress_updated.emit(progress)

            # 保存最终图片
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.manager.filename_prefix}_{timestamp}.{self.manager.file_format.lower()}"
            final_path = os.path.join(self.manager.save_path, filename)

            stitched_image.save(final_path, self.manager.file_format)

            return final_path

        except Exception as e:
            raise Exception(f"拼接图片失败: {str(e)}")

    def remove_overlaps(self, images: List[Image.Image]) -> List[Image.Image]:
        """移除图像间的重叠区域"""
        if len(images) <= 1:
            return images

        processed_images = [images[0]]  # 第一张图片完整保留

        for i in range(1, len(images)):
            current_img = images[i]
            previous_img = images[i-1]

            # 检测重叠区域
            overlap_height = self.detect_overlap(previous_img, current_img)

            if overlap_height > 0:
                # 裁剪掉重叠部分
                cropped_img = current_img.crop((0, overlap_height, current_img.width, current_img.height))
                if cropped_img.height > 0:  # 确保裁剪后还有内容
                    processed_images.append(cropped_img)
            else:
                processed_images.append(current_img)

        return processed_images

    def detect_overlap(self, img1: Image.Image, img2: Image.Image) -> int:
        """检测两张图片的重叠高度"""
        try:
            # 简单的重叠检测：比较img1底部和img2顶部
            overlap_height = min(img1.height // 3, img2.height // 3)  # 最多检测1/3高度

            for h in range(10, overlap_height, 10):  # 每10像素检测一次
                # 获取img1底部区域
                bottom_region = img1.crop((0, img1.height - h, img1.width, img1.height))
                # 获取img2顶部区域
                top_region = img2.crop((0, 0, img2.width, h))

                # 比较两个区域的相似度
                if self.images_similar(bottom_region, top_region):
                    return h

            return 0

        except Exception:
            return 0

    def images_similar(self, img1: Image.Image, img2: Image.Image, threshold: float = 0.95) -> bool:
        """检查两张图片是否相似"""
        try:
            if img1.size != img2.size:
                return False

            from PIL import ImageChops
            diff = ImageChops.difference(img1, img2)
            stat = diff.getextrema()

            # 计算相似度
            max_diff = max([channel[1] for channel in stat])
            similarity = 1 - (max_diff / 255.0)

            return similarity >= threshold

        except Exception:
            return False

    def cleanup_temp_files(self, temp_files: List[str]):
        """清理临时文件"""
        for file_path in temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception:
                pass  # 忽略清理错误
