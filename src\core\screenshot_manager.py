#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
截图管理器 - 负责截图的核心逻辑
"""

import os
import time
from datetime import datetime
from typing import Tuple, List, Optional
from PyQt6.QtCore import QObject, pyqtSignal, QTimer, QThread
from PIL import Image, ImageGrab
import pyautogui


class ScreenshotManager(QObject):
    """截图管理器类"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)  # 进度更新信号
    status_updated = pyqtSignal(str)    # 状态更新信号
    log_message = pyqtSignal(str)       # 日志消息信号
    capture_completed = pyqtSignal(str) # 截图完成信号
    error_occurred = pyqtSignal(str)    # 错误发生信号
    
    def __init__(self):
        super().__init__()
        self.is_capturing = False
        self.is_paused = False
        self.capture_thread = None
        self.screenshots = []  # 存储截图列表
        
        # 截图参数
        self.scroll_speed = 5
        self.scroll_step = 100
        self.scroll_interval = 500
        self.capture_mode = "全屏截图"
        self.save_path = os.path.expanduser("~/Desktop")
        self.filename_prefix = "scroll_capture"
        self.file_format = "PNG"
        self.capture_area = None  # (x, y, width, height)
        
        # 初始化pyautogui设置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
    def set_scroll_settings(self, speed: int, step: int, interval: int):
        """设置滚动参数"""
        self.scroll_speed = speed
        self.scroll_step = step
        self.scroll_interval = interval
        
    def set_capture_mode(self, mode: str):
        """设置截图模式"""
        self.capture_mode = mode
        
    def set_capture_area(self, area: Optional[Tuple[int, int, int, int]]):
        """设置截图区域"""
        self.capture_area = area
        
    def set_save_settings(self, path: str, prefix: str, format_type: str):
        """设置保存参数"""
        self.save_path = path
        self.filename_prefix = prefix
        self.file_format = format_type
        
    def start_capture(self):
        """开始截图"""
        if self.is_capturing:
            return
            
        self.is_capturing = True
        self.is_paused = False
        self.screenshots.clear()
        
        # 创建并启动截图线程
        self.capture_thread = CaptureThread(self)
        self.capture_thread.screenshot_taken.connect(self.on_screenshot_taken)
        self.capture_thread.capture_finished.connect(self.on_capture_finished)
        self.capture_thread.error_occurred.connect(self.on_error_occurred)
        self.capture_thread.start()
        
        self.log_message.emit("开始滚动截图")
        
    def pause_capture(self):
        """暂停/继续截图"""
        if not self.is_capturing:
            return
            
        self.is_paused = not self.is_paused
        if self.is_paused:
            self.log_message.emit("截图已暂停")
        else:
            self.log_message.emit("继续截图")
            
    def stop_capture(self):
        """停止截图"""
        if not self.is_capturing:
            return
            
        self.is_capturing = False
        self.is_paused = False
        
        if self.capture_thread and self.capture_thread.isRunning():
            self.capture_thread.terminate()
            self.capture_thread.wait()
            
        self.log_message.emit("截图已停止")
        
    def on_screenshot_taken(self, screenshot_path: str):
        """处理单张截图完成"""
        self.screenshots.append(screenshot_path)
        self.log_message.emit(f"截取第 {len(self.screenshots)} 张图片")
        
    def on_capture_finished(self, final_image_path: str):
        """处理截图完成"""
        self.is_capturing = False
        self.is_paused = False
        self.capture_completed.emit(final_image_path)
        self.log_message.emit(f"截图完成，保存至: {final_image_path}")
        
    def on_error_occurred(self, error_message: str):
        """处理错误"""
        self.is_capturing = False
        self.is_paused = False
        self.error_occurred.emit(error_message)
        self.log_message.emit(f"错误: {error_message}")


class CaptureThread(QThread):
    """截图线程类"""
    
    screenshot_taken = pyqtSignal(str)
    capture_finished = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, manager: ScreenshotManager):
        super().__init__()
        self.manager = manager
        
    def run(self):
        """线程运行方法"""
        try:
            self.perform_scroll_capture()
        except Exception as e:
            self.error_occurred.emit(str(e))
            
    def perform_scroll_capture(self):
        """执行滚动截图"""
        screenshots = []
        screenshot_count = 0
        
        # 等待用户准备
        time.sleep(2)
        
        # 获取初始屏幕位置
        initial_pos = pyautogui.position()
        
        try:
            while self.manager.is_capturing:
                # 检查暂停状态
                while self.manager.is_paused and self.manager.is_capturing:
                    time.sleep(0.1)
                    
                if not self.manager.is_capturing:
                    break
                    
                # 截取当前屏幕
                screenshot = self.take_screenshot()
                if screenshot:
                    # 保存临时截图
                    temp_path = os.path.join(
                        self.manager.save_path,
                        f"temp_{screenshot_count:04d}.png"
                    )
                    screenshot.save(temp_path)
                    screenshots.append(temp_path)
                    screenshot_count += 1
                    
                    self.screenshot_taken.emit(temp_path)
                
                # 执行滚动
                if not self.scroll_down():
                    # 无法继续滚动，结束截图
                    break
                    
                # 等待滚动完成
                time.sleep(self.manager.scroll_interval / 1000.0)
                
                # 检查是否到达页面底部（简单检测）
                if screenshot_count > 100:  # 防止无限循环
                    break
                    
            # 拼接所有截图
            if screenshots:
                final_image_path = self.stitch_screenshots(screenshots)
                self.cleanup_temp_files(screenshots)
                self.capture_finished.emit(final_image_path)
            else:
                self.error_occurred.emit("未能截取任何图片")
                
        except Exception as e:
            self.cleanup_temp_files(screenshots)
            raise e
            
    def take_screenshot(self) -> Optional[Image.Image]:
        """截取屏幕"""
        try:
            if self.manager.capture_mode == "全屏截图":
                return ImageGrab.grab()
            elif self.manager.capture_mode == "自定义区域" and self.manager.capture_area:
                x, y, w, h = self.manager.capture_area
                return ImageGrab.grab(bbox=(x, y, x + w, y + h))
            else:
                # 默认全屏
                return ImageGrab.grab()
        except Exception as e:
            self.error_occurred.emit(f"截图失败: {str(e)}")
            return None
            
    def scroll_down(self) -> bool:
        """向下滚动"""
        try:
            # 使用pyautogui滚动
            pyautogui.scroll(-self.manager.scroll_step)
            return True
        except Exception as e:
            self.error_occurred.emit(f"滚动失败: {str(e)}")
            return False
            
    def stitch_screenshots(self, screenshot_paths: List[str]) -> str:
        """拼接截图"""
        try:
            if not screenshot_paths:
                raise ValueError("没有截图需要拼接")
                
            # 加载所有图片
            images = []
            for path in screenshot_paths:
                img = Image.open(path)
                images.append(img)
                
            # 计算拼接后的图片尺寸
            total_height = sum(img.height for img in images)
            max_width = max(img.width for img in images)
            
            # 创建拼接后的图片
            stitched_image = Image.new('RGB', (max_width, total_height))
            
            # 拼接图片
            y_offset = 0
            for img in images:
                stitched_image.paste(img, (0, y_offset))
                y_offset += img.height
                
            # 保存最终图片
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.manager.filename_prefix}_{timestamp}.{self.manager.file_format.lower()}"
            final_path = os.path.join(self.manager.save_path, filename)
            
            stitched_image.save(final_path, self.manager.file_format)
            
            return final_path
            
        except Exception as e:
            raise Exception(f"拼接图片失败: {str(e)}")
            
    def cleanup_temp_files(self, temp_files: List[str]):
        """清理临时文件"""
        for file_path in temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception:
                pass  # 忽略清理错误
