@echo off
chcp 65001 > nul
title 滚动截图工具 - 安装依赖

echo ========================================
echo 滚动截图工具 - 依赖安装脚本
echo ========================================
echo.

REM 检查Python是否安装
echo [1/4] 检查Python环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo ✗ 未找到Python，请先安装Python 3.8或更高版本
    echo   下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
) else (
    python --version
    echo ✓ Python环境检查通过
)
echo.

REM 检查pip是否可用
echo [2/4] 检查pip工具...
pip --version > nul 2>&1
if errorlevel 1 (
    echo ✗ pip工具不可用
    pause
    exit /b 1
) else (
    echo ✓ pip工具可用
)
echo.

REM 升级pip
echo [3/4] 升级pip到最新版本...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo ⚠ pip升级失败，继续安装依赖...
) else (
    echo ✓ pip升级完成
)
echo.

REM 安装依赖包
echo [4/4] 安装项目依赖包...
echo 正在安装：PyQt6, Pillow, pyautogui, pygetwindow, pynput
echo.
pip install -r requirements.txt
if errorlevel 1 (
    echo ✗ 依赖包安装失败
    echo.
    echo 请尝试手动安装：
    echo   pip install PyQt6==6.6.1
    echo   pip install Pillow==10.1.0
    echo   pip install pyautogui==0.9.54
    echo   pip install pygetwindow==0.0.9
    echo   pip install pynput==1.7.6
    echo.
    pause
    exit /b 1
) else (
    echo ✓ 所有依赖包安装完成
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 现在您可以：
echo 1. 双击 run.bat 启动程序
echo 2. 或者运行 python main.py
echo 3. 或者运行 python test_gui.py 测试界面
echo.
pause
