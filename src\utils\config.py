#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
import json
from typing import Dict, Any


class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config_data = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                pass
        
        # 返回默认配置
        return self.get_default_config()
        
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置失败: {e}")
            
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "scroll_settings": {
                "speed": 5,
                "step": 100,
                "interval": 500
            },
            "capture_settings": {
                "mode": "全屏截图",
                "area": None
            },
            "save_settings": {
                "path": os.path.expanduser("~/Desktop"),
                "filename_prefix": "scroll_capture",
                "format": "PNG"
            },
            "window_settings": {
                "geometry": None,
                "always_on_top": False
            }
        }
        
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        data = self.config_data
        
        for k in keys:
            if isinstance(data, dict) and k in data:
                data = data[k]
            else:
                return default
                
        return data
        
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        data = self.config_data
        
        for k in keys[:-1]:
            if k not in data:
                data[k] = {}
            data = data[k]
            
        data[keys[-1]] = value
        
    def update_scroll_settings(self, speed: int, step: int, interval: int):
        """更新滚动设置"""
        self.set("scroll_settings.speed", speed)
        self.set("scroll_settings.step", step)
        self.set("scroll_settings.interval", interval)
        
    def update_capture_settings(self, mode: str, area=None):
        """更新截图设置"""
        self.set("capture_settings.mode", mode)
        self.set("capture_settings.area", area)
        
    def update_save_settings(self, path: str, prefix: str, format_type: str):
        """更新保存设置"""
        self.set("save_settings.path", path)
        self.set("save_settings.filename_prefix", prefix)
        self.set("save_settings.format", format_type)
