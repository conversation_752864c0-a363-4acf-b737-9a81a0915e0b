#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI测试脚本 - 用于测试界面是否正常显示
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    print("✓ PyQt6 导入成功")
except ImportError as e:
    print(f"✗ PyQt6 导入失败: {e}")
    print("请运行: pip install PyQt6")
    sys.exit(1)

try:
    from PIL import Image
    print("✓ Pillow 导入成功")
except ImportError as e:
    print(f"✗ Pillow 导入失败: {e}")
    print("请运行: pip install Pillow")
    sys.exit(1)

try:
    import pyautogui
    print("✓ pyautogui 导入成功")
except ImportError as e:
    print(f"✗ pyautogui 导入失败: {e}")
    print("请运行: pip install pyautogui")
    sys.exit(1)

try:
    from gui.main_window import MainWindow
    print("✓ 主窗口模块导入成功")
except ImportError as e:
    print(f"✗ 主窗口模块导入失败: {e}")
    sys.exit(1)

def test_gui():
    """测试GUI界面"""
    print("\n开始测试GUI界面...")
    
    app = QApplication(sys.argv)
    app.setApplicationName("滚动截图工具测试")
    
    try:
        # 创建主窗口
        main_window = MainWindow()
        print("✓ 主窗口创建成功")
        
        # 显示窗口
        main_window.show()
        print("✓ 窗口显示成功")
        
        print("\n测试完成！窗口已显示，请检查界面是否正常。")
        print("关闭窗口即可退出测试。")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    test_gui()
