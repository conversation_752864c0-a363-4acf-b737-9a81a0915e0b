#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口选择器 - 用于选择要截图的窗口
"""

import sys
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap

try:
    import pygetwindow as gw
except ImportError:
    gw = None


class WindowSelector(QDialog):
    """窗口选择对话框"""
    
    window_selected = pyqtSignal(object)  # 发送选中的窗口对象
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_window = None
        self.init_ui()
        self.load_windows()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("选择窗口")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 说明标签
        info_label = QLabel("请选择要截图的窗口:")
        info_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(info_label)
        
        # 窗口列表
        self.window_list = QListWidget()
        self.window_list.setAlternatingRowColors(True)
        self.window_list.itemDoubleClicked.connect(self.on_window_double_clicked)
        layout.addWidget(self.window_list)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新列表")
        self.refresh_btn.clicked.connect(self.load_windows)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.select_btn = QPushButton("选择")
        self.select_btn.clicked.connect(self.on_select_clicked)
        self.select_btn.setEnabled(False)
        button_layout.addWidget(self.select_btn)
        
        layout.addLayout(button_layout)
        
        # 连接选择事件
        self.window_list.itemSelectionChanged.connect(self.on_selection_changed)
        
    def load_windows(self):
        """加载窗口列表"""
        self.window_list.clear()
        
        if gw is None:
            item = QListWidgetItem("错误: 未安装 pygetwindow 库")
            item.setFlags(Qt.ItemFlag.NoItemFlags)
            self.window_list.addItem(item)
            return
            
        try:
            # 获取所有窗口
            windows = gw.getAllWindows()
            
            # 过滤有效窗口
            valid_windows = []
            for window in windows:
                # 过滤掉无标题、最小化或隐藏的窗口
                if (window.title and 
                    window.title.strip() and 
                    window.visible and 
                    window.width > 100 and 
                    window.height > 100):
                    valid_windows.append(window)
                    
            if not valid_windows:
                item = QListWidgetItem("未找到可用的窗口")
                item.setFlags(Qt.ItemFlag.NoItemFlags)
                self.window_list.addItem(item)
                return
                
            # 添加窗口到列表
            for window in valid_windows:
                item_text = f"{window.title} ({window.width}x{window.height})"
                item = QListWidgetItem(item_text)
                item.setData(Qt.ItemDataRole.UserRole, window)
                
                # 添加窗口图标（如果可能）
                try:
                    # 这里可以添加获取窗口图标的代码
                    pass
                except:
                    pass
                    
                self.window_list.addItem(item)
                
        except Exception as e:
            item = QListWidgetItem(f"获取窗口列表失败: {str(e)}")
            item.setFlags(Qt.ItemFlag.NoItemFlags)
            self.window_list.addItem(item)
            
    def on_selection_changed(self):
        """选择改变事件"""
        current_item = self.window_list.currentItem()
        if current_item and current_item.data(Qt.ItemDataRole.UserRole):
            self.selected_window = current_item.data(Qt.ItemDataRole.UserRole)
            self.select_btn.setEnabled(True)
        else:
            self.selected_window = None
            self.select_btn.setEnabled(False)
            
    def on_window_double_clicked(self, item):
        """窗口双击事件"""
        if item.data(Qt.ItemDataRole.UserRole):
            self.selected_window = item.data(Qt.ItemDataRole.UserRole)
            self.accept()
            
    def on_select_clicked(self):
        """选择按钮点击事件"""
        if self.selected_window:
            self.accept()
        else:
            QMessageBox.warning(self, "警告", "请先选择一个窗口")
            
    def get_selected_window(self):
        """获取选中的窗口"""
        return self.selected_window


class WindowCaptureHelper:
    """窗口截图助手"""
    
    @staticmethod
    def get_window_rect(window):
        """获取窗口矩形区域"""
        try:
            return (window.left, window.top, window.width, window.height)
        except Exception as e:
            raise Exception(f"获取窗口位置失败: {str(e)}")
            
    @staticmethod
    def bring_window_to_front(window):
        """将窗口置于前台"""
        try:
            if hasattr(window, 'activate'):
                window.activate()
            elif hasattr(window, 'restore'):
                window.restore()
            return True
        except Exception as e:
            print(f"激活窗口失败: {e}")
            return False
            
    @staticmethod
    def is_window_valid(window):
        """检查窗口是否有效"""
        try:
            return (window and 
                    hasattr(window, 'title') and 
                    hasattr(window, 'visible') and
                    window.visible and
                    window.width > 0 and 
                    window.height > 0)
        except:
            return False


def test_window_selector():
    """测试窗口选择器"""
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    def on_window_selected(window):
        print(f"选择的窗口: {window.title}")
        print(f"位置: ({window.left}, {window.top})")
        print(f"尺寸: {window.width}x{window.height}")
        
    selector = WindowSelector()
    
    if selector.exec() == QDialog.DialogCode.Accepted:
        window = selector.get_selected_window()
        if window:
            on_window_selected(window)
        else:
            print("未选择窗口")
    else:
        print("取消选择")


if __name__ == "__main__":
    test_window_selector()
