# 滚动截图工具 - 详细使用指南

## 🚀 快速开始

### 1. 环境准备
确保您的系统满足以下要求：
- Windows 操作系统
- Python 3.8 或更高版本
- 足够的磁盘空间保存截图

### 2. 安装步骤
```bash
# 1. 安装依赖包
pip install -r requirements.txt

# 2. 运行程序
python main.py
```

### 3. 首次使用
1. 启动程序后，界面会显示各种设置选项
2. 建议先使用默认设置进行测试
3. 选择一个简单的网页或文档进行第一次截图

## 📋 功能详解

### 滚动设置区域

#### 滚动速度 (1-10级)
- **1-3级 (慢速)**：适合内容复杂的页面，如图表、代码等
- **4-6级 (中速)**：适合一般网页和文档
- **7-10级 (快速)**：适合简单文本页面

#### 滚动步长 (50-500像素)
- **50-100px**：精细滚动，适合高精度截图
- **100-200px**：标准滚动，适合大多数情况
- **200-500px**：快速滚动，适合长页面

#### 滚动间隔 (100-2000毫秒)
- **100-300ms**：快速截图，可能错过动态内容
- **500-800ms**：标准间隔，平衡速度和质量
- **1000-2000ms**：慢速截图，确保内容完全加载

### 截图模式选择

#### 1. 全屏截图
- **适用场景**：截取整个屏幕内容
- **优点**：简单易用，无需额外设置
- **注意**：会包含其他窗口和桌面内容

#### 2. 窗口截图
- **适用场景**：只截取特定应用窗口
- **使用方法**：
  1. 选择"窗口截图"模式
  2. 点击"选择窗口"按钮
  3. 从列表中选择目标窗口
- **优点**：只截取需要的内容，避免干扰

#### 3. 自定义区域
- **适用场景**：截取屏幕的特定区域
- **使用方法**：
  1. 选择"自定义区域"模式
  2. 点击"选择截图区域"按钮
  3. 在屏幕上拖拽选择区域
  4. 按Enter确认或ESC取消
- **优点**：精确控制截图范围

### 保存设置

#### 保存路径
- 默认保存到桌面
- 可点击"浏览"按钮选择其他文件夹
- 确保选择的路径有足够空间

#### 文件名前缀
- 默认为"scroll_capture"
- 最终文件名格式：`前缀_时间戳.格式`
- 例如：`scroll_capture_20241129_143022.png`

#### 文件格式
- **PNG**：无损压缩，质量最高，文件较大
- **JPEG**：有损压缩，文件较小，适合分享
- **BMP**：无压缩，文件最大，质量最高

## 🎯 使用技巧

### 最佳实践

#### 截图前准备
1. **关闭不必要的程序**：减少系统负载
2. **调整窗口大小**：确保内容完整显示
3. **检查网络连接**：确保页面内容完全加载
4. **清理桌面**：避免干扰（全屏模式）

#### 参数调优
1. **复杂页面**：降低滚动速度，增加间隔时间
2. **简单页面**：可以提高速度，减少等待时间
3. **长页面**：使用较大的滚动步长
4. **短页面**：使用较小的滚动步长

#### 质量优化
1. **确保页面稳定**：等待页面完全加载后再开始
2. **避免鼠标干扰**：截图过程中不要移动鼠标
3. **关闭动画效果**：减少页面动态元素
4. **使用合适的分辨率**：过高分辨率会增加处理时间

### 常见场景应用

#### 网页截图
```
推荐设置：
- 滚动速度：5 (中等)
- 滚动步长：150px
- 滚动间隔：600ms
- 模式：窗口截图或自定义区域
```

#### 文档截图
```
推荐设置：
- 滚动速度：6 (较快)
- 滚动步长：200px
- 滚动间隔：400ms
- 模式：窗口截图
```

#### 代码截图
```
推荐设置：
- 滚动速度：3 (较慢)
- 滚动步长：100px
- 滚动间隔：800ms
- 模式：自定义区域
```

#### 聊天记录截图
```
推荐设置：
- 滚动速度：4 (慢速)
- 滚动步长：120px
- 滚动间隔：500ms
- 模式：窗口截图
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 程序无法启动
**问题**：双击main.py没有反应或报错
**解决方案**：
- 检查Python是否正确安装
- 运行 `python --version` 确认版本
- 重新安装依赖：`pip install -r requirements.txt`

#### 2. 截图失败
**问题**：点击开始后没有截图或报错
**解决方案**：
- 确保目标窗口处于活动状态
- 检查是否有屏幕录制权限
- 尝试以管理员身份运行程序

#### 3. 滚动不正常
**问题**：页面不滚动或滚动异常
**解决方案**：
- 确保鼠标焦点在目标窗口内
- 尝试手动滚动确认页面可以滚动
- 调整滚动步长和间隔

#### 4. 拼接效果不佳
**问题**：最终图片有重叠或缺失
**解决方案**：
- 降低滚动速度
- 增加滚动间隔时间
- 确保页面内容稳定

#### 5. 文件保存失败
**问题**：截图完成但文件未保存
**解决方案**：
- 检查保存路径是否存在
- 确认磁盘空间充足
- 检查文件夹写入权限

### 性能优化建议

#### 系统优化
1. **关闭不必要的程序**：释放内存和CPU资源
2. **使用SSD硬盘**：提高文件读写速度
3. **增加内存**：处理大图片时需要更多内存

#### 软件设置
1. **合理设置参数**：根据实际需求调整
2. **选择合适的格式**：PNG质量高但文件大，JPEG文件小但有损
3. **定期清理临时文件**：程序会自动清理，但可手动检查

## 📞 获取帮助

### 日志信息
程序底部的日志区域会显示详细的操作信息和错误提示，遇到问题时请查看日志内容。

### 测试功能
运行 `python test_gui.py` 可以测试界面是否正常工作。

### 技术支持
如果遇到无法解决的问题，请：
1. 记录详细的错误信息
2. 说明操作步骤
3. 提供系统环境信息

---

**提示**：首次使用建议先在简单页面上测试，熟悉各项功能后再处理复杂内容。
