# 滚动截图工具

一个基于 Python + PyQt6 的桌面端滚动截图工具，支持自动滚动页面并拼接成长图。

## 功能特性

- 🖼️ **多种截图模式**：支持全屏截图、窗口截图、自定义区域截图
- ⚡ **可调节滚动速度**：支持自定义滚动速度、步长和间隔
- 🎯 **智能拼接**：自动拼接多张截图为一张长图
- 💾 **灵活保存**：支持多种图片格式（PNG、JPEG、BMP）
- 🎮 **实时控制**：支持开始、暂停、停止操作
- 📊 **进度显示**：实时显示截图进度和状态
- 📝 **日志记录**：详细的操作日志记录

## 项目结构

```
A-gundongjieping-tool/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── config.json            # 配置文件（运行后自动生成）
└── src/                   # 源代码目录
    ├── __init__.py
    ├── gui/               # GUI界面模块
    │   ├── __init__.py
    │   └── main_window.py # 主窗口界面
    ├── core/              # 核心功能模块
    │   ├── __init__.py
    │   └── screenshot_manager.py # 截图管理器
    └── utils/             # 工具模块
        ├── __init__.py
        └── config.py      # 配置管理
```

## 安装和运行

### 1. 环境要求

- Python 3.8 或更高版本
- Windows 操作系统

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 运行程序

```bash
python main.py
```

## 使用说明

### 1. 启动程序
双击运行 `main.py` 或在命令行中执行 `python main.py`

### 2. 配置参数
- **滚动设置**：调整滚动速度、步长和间隔
- **截图区域**：选择全屏、窗口或自定义区域
- **保存设置**：设置保存路径、文件名前缀和格式

### 3. 开始截图
1. 点击"开始截图"按钮
2. 程序会自动开始滚动并截图
3. 可以随时暂停或停止截图
4. 截图完成后会自动拼接并保存

### 4. 查看结果
截图完成后，拼接好的长图会保存到指定路径，文件名格式为：`{前缀}_{时间戳}.{格式}`

## 依赖包说明

- **PyQt6**: GUI框架，用于创建桌面应用界面
- **Pillow**: 图像处理库，用于截图和图像拼接
- **pyautogui**: 自动化库，用于模拟鼠标滚动
- **pygetwindow**: 窗口管理库，用于获取窗口信息
- **pynput**: 输入监听库，用于键盘鼠标事件处理

## 开发说明

### 主要模块

1. **main.py**: 程序入口，负责应用程序初始化
2. **gui/main_window.py**: 主窗口界面，包含所有UI组件
3. **core/screenshot_manager.py**: 截图核心逻辑，负责滚动和截图
4. **utils/config.py**: 配置管理，负责保存和加载用户设置

### 扩展功能

- [ ] 区域选择功能实现
- [ ] 窗口截图模式实现
- [ ] 快捷键支持
- [ ] 截图预览功能
- [ ] 批量处理功能

## 注意事项

1. 首次运行时，可能需要授予屏幕录制权限
2. 滚动截图时，请确保目标窗口处于活动状态
3. 建议在截图前关闭不必要的程序，以提高性能
4. 大型页面截图可能需要较长时间，请耐心等待

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。
