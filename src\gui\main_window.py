#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
"""

import os
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QSlider, QSpinBox, QPushButton, QLineEdit, QFileDialog,
    QGroupBox, QComboBox, QProgressBar, QTextEdit, QCheckBox,
    QMessageBox, QFrame
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QIcon, QPalette

from core.screenshot_manager import ScreenshotManager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.screenshot_manager = ScreenshotManager()
        self.init_ui()
        self.connect_signals()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("滚动截图工具 v1.0")
        self.setFixedSize(500, 650)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 添加各个功能区域
        self.create_scroll_settings_group(main_layout)
        self.create_capture_area_group(main_layout)
        self.create_save_settings_group(main_layout)
        self.create_control_buttons(main_layout)
        self.create_progress_area(main_layout)
        self.create_log_area(main_layout)
        
        # 设置样式
        self.set_styles()
        
    def create_scroll_settings_group(self, parent_layout):
        """创建滚动设置组"""
        group = QGroupBox("滚动设置")
        layout = QGridLayout(group)
        
        # 滚动速度设置
        layout.addWidget(QLabel("滚动速度:"), 0, 0)
        self.speed_slider = QSlider(Qt.Orientation.Horizontal)
        self.speed_slider.setRange(1, 10)
        self.speed_slider.setValue(5)
        self.speed_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.speed_slider.setTickInterval(1)
        layout.addWidget(self.speed_slider, 0, 1)
        
        self.speed_label = QLabel("中等")
        layout.addWidget(self.speed_label, 0, 2)
        
        # 滚动步长设置
        layout.addWidget(QLabel("滚动步长(像素):"), 1, 0)
        self.step_spinbox = QSpinBox()
        self.step_spinbox.setRange(50, 500)
        self.step_spinbox.setValue(100)
        self.step_spinbox.setSuffix(" px")
        layout.addWidget(self.step_spinbox, 1, 1, 1, 2)
        
        # 滚动间隔设置
        layout.addWidget(QLabel("滚动间隔(毫秒):"), 2, 0)
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(100, 2000)
        self.interval_spinbox.setValue(500)
        self.interval_spinbox.setSuffix(" ms")
        layout.addWidget(self.interval_spinbox, 2, 1, 1, 2)
        
        parent_layout.addWidget(group)
        
    def create_capture_area_group(self, parent_layout):
        """创建截图区域设置组"""
        group = QGroupBox("截图区域")
        layout = QVBoxLayout(group)
        
        # 截图模式选择
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("截图模式:"))
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["全屏截图", "窗口截图", "自定义区域"])
        mode_layout.addWidget(self.mode_combo)
        mode_layout.addStretch()
        layout.addLayout(mode_layout)
        
        # 区域选择按钮
        self.select_area_btn = QPushButton("选择截图区域")
        self.select_area_btn.setEnabled(False)
        layout.addWidget(self.select_area_btn)
        
        parent_layout.addWidget(group)
        
    def create_save_settings_group(self, parent_layout):
        """创建保存设置组"""
        group = QGroupBox("保存设置")
        layout = QGridLayout(group)
        
        # 保存路径
        layout.addWidget(QLabel("保存路径:"), 0, 0)
        self.save_path_edit = QLineEdit()
        self.save_path_edit.setText(os.path.expanduser("~/Desktop"))
        layout.addWidget(self.save_path_edit, 0, 1)
        
        self.browse_btn = QPushButton("浏览")
        layout.addWidget(self.browse_btn, 0, 2)
        
        # 文件名前缀
        layout.addWidget(QLabel("文件名前缀:"), 1, 0)
        self.filename_edit = QLineEdit()
        self.filename_edit.setText("scroll_capture")
        layout.addWidget(self.filename_edit, 1, 1)
        
        # 文件格式
        layout.addWidget(QLabel("文件格式:"), 1, 2)
        self.format_combo = QComboBox()
        self.format_combo.addItems(["PNG", "JPEG", "BMP"])
        layout.addWidget(self.format_combo, 1, 3)
        
        parent_layout.addWidget(group)
        
    def create_control_buttons(self, parent_layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始截图")
        self.start_btn.setMinimumHeight(40)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        
        self.pause_btn = QPushButton("暂停")
        self.pause_btn.setMinimumHeight(40)
        self.pause_btn.setEnabled(False)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setMinimumHeight(40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
        """)
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.pause_btn)
        button_layout.addWidget(self.stop_btn)
        
        parent_layout.addLayout(button_layout)
        
    def create_progress_area(self, parent_layout):
        """创建进度显示区域"""
        group = QGroupBox("进度")
        layout = QVBoxLayout(group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        parent_layout.addWidget(group)
        
    def create_log_area(self, parent_layout):
        """创建日志显示区域"""
        group = QGroupBox("日志")
        layout = QVBoxLayout(group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        parent_layout.addWidget(group)
        
    def connect_signals(self):
        """连接信号和槽"""
        # 滚动速度滑块
        self.speed_slider.valueChanged.connect(self.update_speed_label)

        # 截图模式改变
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)

        # 按钮点击事件
        self.browse_btn.clicked.connect(self.browse_save_path)
        self.select_area_btn.clicked.connect(self.select_capture_area)
        self.start_btn.clicked.connect(self.start_capture)
        self.pause_btn.clicked.connect(self.pause_capture)
        self.stop_btn.clicked.connect(self.stop_capture)

        # 连接截图管理器信号
        self.screenshot_manager.progress_updated.connect(self.progress_bar.setValue)
        self.screenshot_manager.status_updated.connect(self.status_label.setText)
        self.screenshot_manager.log_message.connect(self.add_log)
        self.screenshot_manager.capture_completed.connect(self.on_capture_completed)
        self.screenshot_manager.error_occurred.connect(self.on_error_occurred)
        
    def set_styles(self):
        """设置界面样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #e0e0e0;
                border: 1px solid #cccccc;
                border-radius: 3px;
                padding: 5px;
                min-height: 25px;
            }
            QPushButton:hover {
                background-color: #d0d0d0;
            }
            QPushButton:pressed {
                background-color: #c0c0c0;
            }
            QPushButton:disabled {
                background-color: #f5f5f5;
                color: #999999;
            }
        """)
        
    def update_speed_label(self, value):
        """更新速度标签"""
        speed_labels = {
            1: "很慢", 2: "慢", 3: "较慢", 4: "慢速", 5: "中等",
            6: "较快", 7: "快速", 8: "快", 9: "很快", 10: "极快"
        }
        self.speed_label.setText(speed_labels.get(value, "中等"))
        
    def on_mode_changed(self, mode):
        """截图模式改变时的处理"""
        self.select_area_btn.setEnabled(mode == "自定义区域")
        
    def browse_save_path(self):
        """浏览保存路径"""
        path = QFileDialog.getExistingDirectory(
            self, "选择保存路径", self.save_path_edit.text()
        )
        if path:
            self.save_path_edit.setText(path)
            
    def select_capture_area(self):
        """选择截图区域"""
        # TODO: 实现区域选择功能
        self.add_log("区域选择功能待实现")
        
    def start_capture(self):
        """开始截图"""
        # 更新界面状态
        self.start_btn.setEnabled(False)
        self.pause_btn.setEnabled(True)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在截图...")

        # 更新截图管理器设置
        self.screenshot_manager.set_scroll_settings(
            self.speed_slider.value(),
            self.step_spinbox.value(),
            self.interval_spinbox.value()
        )

        self.screenshot_manager.set_capture_mode(self.mode_combo.currentText())

        self.screenshot_manager.set_save_settings(
            self.save_path_edit.text(),
            self.filename_edit.text(),
            self.format_combo.currentText()
        )

        # 启动截图
        self.screenshot_manager.start_capture()
        
    def pause_capture(self):
        """暂停截图"""
        self.screenshot_manager.pause_capture()
        if self.pause_btn.text() == "暂停":
            self.pause_btn.setText("继续")
            self.status_label.setText("已暂停")
        else:
            self.pause_btn.setText("暂停")
            self.status_label.setText("正在截图...")

    def stop_capture(self):
        """停止截图"""
        self.screenshot_manager.stop_capture()
        self.reset_ui_state()

    def reset_ui_state(self):
        """重置界面状态"""
        self.start_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)
        self.pause_btn.setText("暂停")
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")

    def on_capture_completed(self, file_path):
        """截图完成处理"""
        self.reset_ui_state()
        QMessageBox.information(
            self, "截图完成",
            f"滚动截图已完成！\n\n保存位置：{file_path}"
        )

    def on_error_occurred(self, error_message):
        """错误处理"""
        self.reset_ui_state()
        QMessageBox.critical(
            self, "错误",
            f"截图过程中发生错误：\n\n{error_message}"
        )
        
    def add_log(self, message):
        """添加日志信息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
